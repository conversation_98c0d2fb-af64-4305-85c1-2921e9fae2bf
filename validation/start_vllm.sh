#!/bin/bash

# Final vLLM Service Startup Script for Vidur vs vLLM Comparison Experiment
# This script reads all configuration from the shared YAML config file to ensure
# consistent parameters between Vidur simulation and vLLM serving

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/vidur_simulation_config.yaml"

echo -e "${BLUE}=== vLLM Service Startup (Vidur vs vLLM Comparison) ===${NC}"
echo ""

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}Error: Configuration file not found at $CONFIG_FILE${NC}"
    exit 1
fi

# Check if yq is available for YAML parsing
if ! command -v yq &> /dev/null; then
    echo -e "${RED}Error: yq is required to parse YAML configuration${NC}"
    echo "Please install yq: https://github.com/mikefarah/yq"
    exit 1
fi

echo -e "${YELLOW}Reading configuration from: $CONFIG_FILE${NC}"
echo ""

# Extract shared configuration values
SHARED_MODEL_PATH=$(yq eval '.shared_config.model_path' "$CONFIG_FILE")
SHARED_MODEL_NAME=$(yq eval '.shared_config.model_name' "$CONFIG_FILE")
SHARED_TENSOR_PARALLEL_SIZE=$(yq eval '.shared_config.tensor_parallel_size' "$CONFIG_FILE")
SHARED_MAX_MODEL_LEN=$(yq eval '.shared_config.max_model_len' "$CONFIG_FILE")
SHARED_GPU_MEMORY_UTILIZATION=$(yq eval '.shared_config.gpu_memory_utilization' "$CONFIG_FILE")

# Extract vLLM-specific configuration values
VLLM_PORT=$(yq eval '.vllm_service.port' "$CONFIG_FILE")
VLLM_HOST=$(yq eval '.vllm_service.host' "$CONFIG_FILE")
VLLM_SERVED_MODEL_NAME=$(yq eval '.vllm_service.served_model_name' "$CONFIG_FILE")
VLLM_TRUST_REMOTE_CODE=$(yq eval '.vllm_service.trust_remote_code' "$CONFIG_FILE")
VLLM_MAX_NUM_SEQS=$(yq eval '.vllm_service.max_num_seqs' "$CONFIG_FILE")
VLLM_MAX_NUM_BATCHED_TOKENS=$(yq eval '.vllm_service.max_num_batched_tokens' "$CONFIG_FILE")
VLLM_DISABLE_LOG_STATS=$(yq eval '.vllm_service.disable_log_stats' "$CONFIG_FILE")
VLLM_ENABLE_PREFIX_CACHING=$(yq eval '.vllm_service.enable_prefix_caching' "$CONFIG_FILE")

# Validate required values
if [ "$SHARED_MODEL_PATH" = "null" ] || [ -z "$SHARED_MODEL_PATH" ]; then
    echo -e "${RED}Error: shared_config.model_path not found in configuration file${NC}"
    exit 1
fi

if [ "$VLLM_PORT" = "null" ] || [ -z "$VLLM_PORT" ]; then
    echo -e "${RED}Error: vllm_service.port not found in configuration file${NC}"
    exit 1
fi

# Check if model path exists
if [ ! -d "$SHARED_MODEL_PATH" ]; then
    echo -e "${RED}Error: Model path does not exist: $SHARED_MODEL_PATH${NC}"
    exit 1
fi

# Display configuration
echo -e "${GREEN}=== Configuration Summary ===${NC}"
echo -e "${BLUE}Shared Configuration:${NC}"
echo "  Model Path: $SHARED_MODEL_PATH"
echo "  Model Name: $SHARED_MODEL_NAME"
echo "  Tensor Parallel Size: $SHARED_TENSOR_PARALLEL_SIZE"
echo "  Max Model Length: $SHARED_MAX_MODEL_LEN"
echo "  GPU Memory Utilization: $SHARED_GPU_MEMORY_UTILIZATION"
echo ""
echo -e "${BLUE}vLLM-Specific Configuration:${NC}"
echo "  Port: $VLLM_PORT"
echo "  Host: $VLLM_HOST"
echo "  Served Model Name: $VLLM_SERVED_MODEL_NAME"
echo "  Trust Remote Code: $VLLM_TRUST_REMOTE_CODE"
echo "  Max Num Seqs: $VLLM_MAX_NUM_SEQS"
echo "  Max Num Batched Tokens: $VLLM_MAX_NUM_BATCHED_TOKENS"
echo "  Disable Log Stats: $VLLM_DISABLE_LOG_STATS"
echo "  Enable Prefix Caching: $VLLM_ENABLE_PREFIX_CACHING"
echo ""

# Build vLLM command using shared and vLLM-specific parameters
VLLM_CMD="vllm serve $SHARED_MODEL_PATH"
VLLM_CMD="$VLLM_CMD --port $VLLM_PORT"
VLLM_CMD="$VLLM_CMD --host $VLLM_HOST"
VLLM_CMD="$VLLM_CMD --served-model-name $VLLM_SERVED_MODEL_NAME"
VLLM_CMD="$VLLM_CMD --max-model-len $SHARED_MAX_MODEL_LEN"
VLLM_CMD="$VLLM_CMD --tensor-parallel-size $SHARED_TENSOR_PARALLEL_SIZE"
VLLM_CMD="$VLLM_CMD --gpu-memory-utilization $SHARED_GPU_MEMORY_UTILIZATION"
VLLM_CMD="$VLLM_CMD --max-num-seqs $VLLM_MAX_NUM_SEQS"
VLLM_CMD="$VLLM_CMD --max-num-batched-tokens $VLLM_MAX_NUM_BATCHED_TOKENS"

# Add boolean flags conditionally
if [ "$VLLM_TRUST_REMOTE_CODE" = "true" ]; then
    VLLM_CMD="$VLLM_CMD --trust-remote-code"
fi

if [ "$VLLM_DISABLE_LOG_STATS" = "true" ]; then
    VLLM_CMD="$VLLM_CMD --disable-log-stats"
fi

if [ "$VLLM_ENABLE_PREFIX_CACHING" = "true" ]; then
    VLLM_CMD="$VLLM_CMD --enable-prefix-caching"
fi

# Display final command
echo -e "${GREEN}=== Starting vLLM Service ===${NC}"
echo -e "${YELLOW}Command:${NC} $VLLM_CMD"
echo ""
echo -e "${BLUE}Note: This configuration ensures consistency with Vidur simulation parameters${NC}"
echo ""

# Start vLLM service
eval $VLLM_CMD
