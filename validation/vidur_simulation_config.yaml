# Vidur Simulation Configuration
# This configuration file contains all the parameters for running vidur simulation
# Based on the example command line from the official documentation

# Replica Configuration
replica_config:
  device: "a100"
  model_name: "meta-llama/Meta-Llama-3-8B"
  local_model_path: "/share_data/llm_weights/Meta-Llama-3-8B"
  tensor_parallel_size: 1
  num_pipeline_stages: 1

# vLLM Service Configuration
vllm_service:
  port: 8010
  model_path: "/share_data/llm_weights/Meta-Llama-3-8B"

# Cluster Configuration  
cluster_config:
  num_replicas: 1

# Request Generator Configuration
request_generator_config:
  type: "synthetic"

# Synthetic Request Generator Configuration
synthetic_request_generator_config:
  num_requests: 512

# Length Generator Configuration
length_generator_config:
  type: "trace"

# Trace Request Length Generator Configuration
trace_request_length_generator_config:
  max_tokens: 16384
  trace_file: "./data/processed_traces/splitwise_conv.csv"

# Interval Generator Configuration
interval_generator_config:
  type: "poisson"

# Poisson Request Interval Generator Configuration
poisson_request_interval_generator_config:
  qps: 6.45

# Replica Scheduler Configuration
replica_scheduler_config:
  type: "sarathi"

# Sarathi Scheduler Configuration
sarathi_scheduler_config:
  batch_size_cap: 512
  chunk_size: 512

# Random Forest Execution Time Predictor Configuration
random_forrest_execution_time_predictor_config:
  prediction_max_prefill_chunk_size: 16384
  prediction_max_batch_size: 512
  prediction_max_tokens_per_request: 16384

# Performance Metrics Available in request_metrics.csv:
# - Request Id: Unique identifier for each request
# - request_e2e_time: End-to-end request time
# - request_e2e_time_normalized: Normalized end-to-end request time
# - request_execution_time: Request execution time
# - request_execution_time_normalized: Normalized request execution time
# - request_model_execution_time: Model execution time for the request
# - request_model_execution_time_normalized: Normalized model execution time
# - request_preemption_time: Time spent in preemption
# - request_scheduling_delay: Scheduling delay for the request
# - request_execution_plus_preemption_time: Combined execution and preemption time
# - request_execution_plus_preemption_time_normalized: Normalized combined time
# - prefill_e2e_time: End-to-end prefill time
# - prefill_time_execution_plus_preemption: Prefill execution plus preemption time
# - prefill_time_execution_plus_preemption_normalized: Normalized prefill time
# - decode_time_execution_plus_preemption_normalized: Normalized decode time
# - request_inter_arrival_delay: Delay between request arrivals
# - request_num_tokens: Total number of tokens in the request
# - request_num_prefill_tokens: Number of prefill tokens
# - request_num_decode_tokens: Number of decode tokens
# - request_pd_ratio: Prefill to decode ratio
# - request_num_restarts: Number of request restarts
