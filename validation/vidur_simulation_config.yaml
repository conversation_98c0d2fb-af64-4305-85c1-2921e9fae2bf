# Vidur vs vLLM Comparison Experiment Configuration
# This configuration file contains shared parameters for both vidur simulation and vLLM serving
# to ensure fair comparison between the two systems

# ============================================================================
# SHARED CONFIGURATION (Used by both Vidur and vLLM)
# ============================================================================
shared_config:
  # Model Configuration
  model_name: "meta-llama/Meta-Llama-3-8B"

  # Hardware Configuration
  device: "a100"
  tensor_parallel_size: 1

  # Model Parameters
  max_model_len: 16384
  max_tokens: 16384

  # Batch Configuration
  max_batch_size: 512
  batch_size_cap: 512
  chunk_size: 512

  # Performance Configuration
  gpu_memory_utilization: 0.9

  # Caching Configuration (Shared by both systems)
  enable_prefix_caching: false
  block_size: 16

# ============================================================================
# VIDUR-SPECIFIC CONFIGURATION
# ============================================================================
# Replica Configuration
replica_config:
  device: "a100"  # References shared_config.device
  model_name: "meta-llama/Meta-Llama-3-8B"  # References shared_config.model_name
  tensor_parallel_size: 1  # References shared_config.tensor_parallel_size
  

# ============================================================================
# VLLM-SPECIFIC CONFIGURATION
# ============================================================================
vllm_service:
  # Network Configuration
  port: 8010
  host: "0.0.0.0"

  # Model Configuration (references shared_config)
  model_path: "/share_data/llm_weights/Meta-Llama-3-8B"
  served_model_name: "Meta-Llama-3-8B"

  # Feature Flags
  trust_remote_code: true
  disable_log_stats: false
  # enable_prefix_caching moved to shared_config

# ============================================================================
# VIDUR SIMULATION CONFIGURATION
# ============================================================================
# Cluster Configuration
cluster_config:
  num_replicas: 1

# Request Generator Configuration
request_generator_config:
  type: "synthetic"

# Synthetic Request Generator Configuration
synthetic_request_generator_config:
  num_requests: 512

# Length Generator Configuration
length_generator_config:
  type: "trace"

# Trace Request Length Generator Configuration
trace_request_length_generator_config:
  max_tokens: 16384  # References shared_config.max_tokens
  trace_file: "./data/processed_traces/splitwise_conv.csv"

# Interval Generator Configuration
interval_generator_config:
  type: "poisson"

# Poisson Request Interval Generator Configuration
poisson_request_interval_generator_config:
  qps: 6.45

# Replica Scheduler Configuration
replica_scheduler_config:
  type: "sarathi"

# Sarathi Scheduler Configuration
sarathi_scheduler_config:
  batch_size_cap: 512  # References shared_config.batch_size_cap
  chunk_size: 512      # References shared_config.chunk_size

# Random Forest Execution Time Predictor Configuration
random_forrest_execution_time_predictor_config:
  prediction_max_prefill_chunk_size: 16384  # References shared_config.max_tokens
  prediction_max_batch_size: 512            # References shared_config.max_batch_size
  prediction_max_tokens_per_request: 16384  # References shared_config.max_tokens

# Performance Metrics Available in request_metrics.csv:
# - Request Id: Unique identifier for each request
# - request_e2e_time: End-to-end request time
# - request_e2e_time_normalized: Normalized end-to-end request time
# - request_execution_time: Request execution time
# - request_execution_time_normalized: Normalized request execution time
# - request_model_execution_time: Model execution time for the request
# - request_model_execution_time_normalized: Normalized model execution time
# - request_preemption_time: Time spent in preemption
# - request_scheduling_delay: Scheduling delay for the request
# - request_execution_plus_preemption_time: Combined execution and preemption time
# - request_execution_plus_preemption_time_normalized: Normalized combined time
# - prefill_e2e_time: End-to-end prefill time
# - prefill_time_execution_plus_preemption: Prefill execution plus preemption time
# - prefill_time_execution_plus_preemption_normalized: Normalized prefill time
# - decode_time_execution_plus_preemption_normalized: Normalized decode time
# - request_inter_arrival_delay: Delay between request arrivals
# - request_num_tokens: Total number of tokens in the request
# - request_num_prefill_tokens: Number of prefill tokens
# - request_num_decode_tokens: Number of decode tokens
# - request_pd_ratio: Prefill to decode ratio
# - request_num_restarts: Number of request restarts
