# Vidur vs vLLM 对比实验配置分析

## 问题回答总结

### 1. 关于 `enable_prefix_caching` 参数

**✅ 您的判断完全正确！**

`enable_prefix_caching` **不是vLLM专有的功能**：

- **vLLM支持**: `--enable-prefix-caching` / `--no-enable-prefix-caching`
  - V0版本默认禁用，V1版本默认启用
- **Vidur支持**: 虽然没有直接的 `enable_prefix_caching` 参数，但有相关缓存机制：
  - `no_cache`: 控制预测模型缓存
  - `kv_cache_prediction_granularity`: KV缓存预测粒度
  - 各种模型预测和KV缓存机制

### 2. 关于vLLM的host配置

从vLLM帮助信息确认：

```bash
--host HOST           Host name. (default: None)
--port PORT           Port number. (default: 8000)
```

- **默认host**: `None` (通常解析为 `localhost` 或 `127.0.0.1`)
- **默认port**: `8000`
- **网络访问推荐**: 使用 `0.0.0.0` 允许外部连接

## 共享配置参数

经过源码分析，以下参数在vidur和vLLM中都支持，应该放在共享配置中：

### 核心共享参数
- `model_name` / `model_path`
- `tensor_parallel_size`
- `max_model_len` / `max_tokens`
- `gpu_memory_utilization`
- `batch_size_cap` / `max_batch_size`
- `block_size`
- `enable_prefix_caching` ✅ **新发现的共享参数**

### 性能相关参数
- `chunk_size` (Sarathi调度器)
- `max_tokens_in_batch`
- 各种缓存配置参数

## 更新后的配置结构

### 共享配置 (shared_config)
```yaml
shared_config:
  # 模型配置
  model_name: "meta-llama/Meta-Llama-3-8B"
  model_path: "/share_data/llm_weights/Meta-Llama-3-8B"
  
  # 硬件配置
  device: "a100"
  tensor_parallel_size: 1
  
  # 模型参数
  max_model_len: 16384
  max_tokens: 16384
  
  # 批处理配置
  max_batch_size: 512
  batch_size_cap: 512
  chunk_size: 512
  
  # 性能配置
  gpu_memory_utilization: 0.9
  
  # 缓存配置 (两个系统都支持)
  enable_prefix_caching: false
  block_size: 16
```

### vLLM专有配置
```yaml
vllm_service:
  # 网络配置
  port: 8010
  host: "0.0.0.0"  # 推荐用于网络访问
  
  # 服务配置
  served_model_name: "Meta-Llama-3-8B"
  trust_remote_code: true
  disable_log_stats: false
  
  # vLLM特有参数
  max_num_seqs: 256
  max_num_batched_tokens: 8192
```

## 启动脚本改进

### 主要改进点：
1. **完全模块化**: 所有参数都从YAML读取
2. **共享参数优先**: `enable_prefix_caching`、`block_size`等从共享配置读取
3. **Host配置说明**: 明确说明默认为localhost，网络访问需要0.0.0.0
4. **参数一致性**: 确保vidur和vLLM使用相同的核心参数

### 使用方法：
```bash
cd validation
./start_vllm.sh
```

## 实验一致性保证

通过这种配置结构，确保：
- ✅ 两个系统使用相同的模型和硬件配置
- ✅ 两个系统使用相同的性能参数
- ✅ 两个系统使用相同的缓存策略
- ✅ 只有系统特有的参数才分别配置

这样可以确保vidur仿真和vLLM实际服务的对比实验具有公平性和可比性。
