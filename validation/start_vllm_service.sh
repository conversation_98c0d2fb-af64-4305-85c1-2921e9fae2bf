#!/bin/bash

# Start vLLM Service Script
# This script reads configuration from vidur_simulation_config.yaml and starts vLLM service

set -e  # Exit on any error

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/vidur_simulation_config.yaml"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file not found at $CONFIG_FILE"
    exit 1
fi

# Check if yq is available for YAML parsing
if ! command -v yq &> /dev/null; then
    echo "Error: yq is required to parse YAML configuration"
    echo "Please install yq: https://github.com/mikefarah/yq"
    exit 1
fi

# Extract configuration values from YAML
MODEL_PATH=$(yq eval '.vllm_service.model_path' "$CONFIG_FILE")
PORT=$(yq eval '.vllm_service.port' "$CONFIG_FILE")

# Validate extracted values
if [ "$MODEL_PATH" = "null" ] || [ -z "$MODEL_PATH" ]; then
    echo "Error: model_path not found in configuration file"
    exit 1
fi

if [ "$PORT" = "null" ] || [ -z "$PORT" ]; then
    echo "Error: port not found in configuration file"
    exit 1
fi

# Check if model path exists
if [ ! -d "$MODEL_PATH" ]; then
    echo "Error: Model path does not exist: $MODEL_PATH"
    exit 1
fi

echo "Starting vLLM service with the following configuration:"
echo "  Model Path: $MODEL_PATH"
echo "  Port: $PORT"
echo "  Config File: $CONFIG_FILE"
echo ""

# Start vLLM service
echo "Launching vLLM service..."
python -m vllm.entrypoints.openai.api_server \
    --model "$MODEL_PATH" \
    --port "$PORT" \
    --host 0.0.0.0 \
    --served-model-name "Meta-Llama-3-8B" \
    --trust-remote-code \
    --max-model-len 16384 \
    --tensor-parallel-size 1

echo "vLLM service started successfully!"
