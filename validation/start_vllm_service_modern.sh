#!/bin/bash

# Start vLLM Service Script (Modern Version using 'vllm serve')
# This script reads configuration from vidur_simulation_config.yaml and starts vLLM service

set -e  # Exit on any error

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/vidur_simulation_config.yaml"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file not found at $CONFIG_FILE"
    exit 1
fi

# Check if yq is available for YAML parsing
if ! command -v yq &> /dev/null; then
    echo "Error: yq is required to parse YAML configuration"
    echo "Please install yq: https://github.com/mikefarah/yq"
    exit 1
fi

# Extract configuration values from YAML
MODEL_PATH=$(yq eval '.vllm_service.model_path' "$CONFIG_FILE")
PORT=$(yq eval '.vllm_service.port' "$CONFIG_FILE")
HOST=$(yq eval '.vllm_service.host' "$CONFIG_FILE")
SERVED_MODEL_NAME=$(yq eval '.vllm_service.served_model_name' "$CONFIG_FILE")
TRUST_REMOTE_CODE=$(yq eval '.vllm_service.trust_remote_code' "$CONFIG_FILE")
MAX_MODEL_LEN=$(yq eval '.vllm_service.max_model_len' "$CONFIG_FILE")
TENSOR_PARALLEL_SIZE=$(yq eval '.vllm_service.tensor_parallel_size' "$CONFIG_FILE")
GPU_MEMORY_UTILIZATION=$(yq eval '.vllm_service.gpu_memory_utilization' "$CONFIG_FILE")
MAX_NUM_SEQS=$(yq eval '.vllm_service.max_num_seqs' "$CONFIG_FILE")
MAX_NUM_BATCHED_TOKENS=$(yq eval '.vllm_service.max_num_batched_tokens' "$CONFIG_FILE")
DISABLE_LOG_STATS=$(yq eval '.vllm_service.disable_log_stats' "$CONFIG_FILE")
ENABLE_PREFIX_CACHING=$(yq eval '.vllm_service.enable_prefix_caching' "$CONFIG_FILE")

# Validate required values
if [ "$MODEL_PATH" = "null" ] || [ -z "$MODEL_PATH" ]; then
    echo "Error: model_path not found in configuration file"
    exit 1
fi

if [ "$PORT" = "null" ] || [ -z "$PORT" ]; then
    echo "Error: port not found in configuration file"
    exit 1
fi

# Check if model path exists
if [ ! -d "$MODEL_PATH" ]; then
    echo "Error: Model path does not exist: $MODEL_PATH"
    exit 1
fi

echo "Starting vLLM service (Modern Method: vllm serve) with the following configuration:"
echo "  Model Path: $MODEL_PATH"
echo "  Port: $PORT"
echo "  Host: $HOST"
echo "  Served Model Name: $SERVED_MODEL_NAME"
echo "  Trust Remote Code: $TRUST_REMOTE_CODE"
echo "  Max Model Length: $MAX_MODEL_LEN"
echo "  Tensor Parallel Size: $TENSOR_PARALLEL_SIZE"
echo "  GPU Memory Utilization: $GPU_MEMORY_UTILIZATION"
echo "  Max Num Seqs: $MAX_NUM_SEQS"
echo "  Max Num Batched Tokens: $MAX_NUM_BATCHED_TOKENS"
echo "  Disable Log Stats: $DISABLE_LOG_STATS"
echo "  Enable Prefix Caching: $ENABLE_PREFIX_CACHING"
echo "  Config File: $CONFIG_FILE"
echo ""

# Build vLLM command with all parameters from YAML (using modern 'vllm serve')
VLLM_CMD="vllm serve $MODEL_PATH"
VLLM_CMD="$VLLM_CMD --port $PORT"
VLLM_CMD="$VLLM_CMD --host $HOST"
VLLM_CMD="$VLLM_CMD --served-model-name $SERVED_MODEL_NAME"
VLLM_CMD="$VLLM_CMD --max-model-len $MAX_MODEL_LEN"
VLLM_CMD="$VLLM_CMD --tensor-parallel-size $TENSOR_PARALLEL_SIZE"
VLLM_CMD="$VLLM_CMD --gpu-memory-utilization $GPU_MEMORY_UTILIZATION"
VLLM_CMD="$VLLM_CMD --max-num-seqs $MAX_NUM_SEQS"
VLLM_CMD="$VLLM_CMD --max-num-batched-tokens $MAX_NUM_BATCHED_TOKENS"

# Add boolean flags conditionally
if [ "$TRUST_REMOTE_CODE" = "true" ]; then
    VLLM_CMD="$VLLM_CMD --trust-remote-code"
fi

if [ "$DISABLE_LOG_STATS" = "true" ]; then
    VLLM_CMD="$VLLM_CMD --disable-log-stats"
fi

if [ "$ENABLE_PREFIX_CACHING" = "true" ]; then
    VLLM_CMD="$VLLM_CMD --enable-prefix-caching"
fi

# Start vLLM service
echo "Launching vLLM service..."
echo "Command: $VLLM_CMD"
echo ""
eval $VLLM_CMD

echo "vLLM service started successfully!"
